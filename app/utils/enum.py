from enum import Enum

class UseCase(str, Enum):
    """产品类型枚举"""
    # 这是项目配置需要用到的模型，场景包括：AI课题名称优化、AI介绍、AI总结服务
    PROJECT_CONFIG_NEED = "PROJECT_CONFIG_NEED"
    # 这是生成大纲、生成报告的默认模型
    PAPER_GENERATE = "PAPER_GENERATE"
    # 扩写、续写、缩写、润色
    HANDLE_TEXT = "HANDLE_TEXT"
    # 脑图、重点服务的默认模型
    INSIGHT_MIND_MAP = "INSIGHT_MIND_MAP"
    # 生成灵感使用的模型
    INSIGHT_GENERATE = "INSIGHT_GENERATE"
    # 灵感里面的扩写、续写、缩写、润色、翻译服务的默认模型
    INSIGHT_HANDLE_TEXT = "INSIGHT_HANDLE_TEXT"
    # 去AI痕迹
    AI_TRACES_REMOVE = "AI_TRACES_REMOVE"
    # 幻觉审查
    HALLUCINATION_REMOVE = "HALLUCINATION"
    # 判断网页内容有用性
    CONCLUDE_WEB_PAGE_USEFUL = "WEB_PAGE_USEFUL"
    # PPT生成
    AI_PPT_GENERATE = "AI_PPT_GENERATE"
class ManagerScene(str, Enum):
    # 去AI痕迹
    AI_TRACES_REMOVE = "AI_TRACES_REMOVE"
    # 幻觉审查
    HALLUCINATION_REMOVE = "HALLUCINATION"
    # 正文生成
    PAPER_GENERATE = "PAPER_GENERATE"
    # 大纲生成
    LAYOUT_GENERATE = "LAYOUT_GENERATE"

class ErrorType(str, Enum):
  """错误枚举"""
  FORBIDDEN = "权限不足"
  FILE_NOT_EXIST = "文档不存在"
  NOT_VALID_TEXT = "没有接收到有效内容"

class ModelConfigError(str, Enum):
  NOT_RECORD = "模型不存在"
  GET_LIST_FAIL = "获取模型列表失败"
  MODEL_IS_DISTRIBUTED="模型已经被分配给机构使用"
  MODEL_EXISTED="模型名称已存在"

class OrganizationError(str, Enum):
  NO_RECORD = "机构不存在"

class ProjectConfigError(str, Enum):
  NOT_RECORD="项目配置不存在"
  NAME_EMPTY="研究主题不能为空"
  NO_HALLUCINATING_REPORT="幻觉审查报告还未生成"
  STATUS_ERROR="材料状态不对"
  UPLOAD_FILE_ERROR = "仅支持上传word、md、txt文件"
  GET_USER_ADD_DEMO_FAIL = "获取用户上传的模板内容失败"

class ProjectLeaderError(str, Enum):
  NOT_RECORD="项目主体不存在"

class CallLLMFlag(str, Enum):
  LEADER_AI_INTRODUCTION = "生成材料主体AI介绍"
  URL_IS_VALID = "评估用户给的网页链接的有效性"
  GENERATE_OUTLINE = "生成大纲"
  GENERATE_REPORT = "生成正文"
  GENERATE_INSIGHT_REPORT = "生成灵感发现报告"

class ProjectUrlSummaryError(str, Enum):
  URL_LIST_IS_EMPTY = "网页链接列表为空"
  STOP_ASYNC_TASK_ERROR = "移除URL处理的异步任务时报错"
  GET_ASYNC_TASK_ERROR = "获取URL异步处理情况报错"
  GET_LIST_FAIL = "获取网站链接列表失败"
  INSERT_BATCH_FAIL = "上传网站链接失败"
  URL_BIND_FAIL = "绑定网站链接到项目失败"
class ProjectReportError(str, Enum):
  NOT_KEY_WORDS = "没有获取到网页搜索的关键词列表"
  KEY_WORDS_LIST = "已生成以下的搜索关键词"
  GET_USER_ADD_URL_FAIL = "获取用户上传的网页链接失败"
  GENERATE_OUTLINE = "生成大纲"
  GENERATE_REPORT = "生成正文"

class OrganizationModelError(str, Enum):
  NOT_MODEL_RECORD = "给机构更新的模型ID不存在"
  ORGANIZATION_ID_EMPTY = "机构ID不能为空"
  LIST_GET_ERROR = "获取机构的模型列表报错"
  CREATED_MODEL_TO_ORG_ERROR = "给机构赋予模型报错"
  CHANGE_DEFAULT_USE_ERROR = "修改机构的默认模型报错"
  ORG_NO_MODEL = "机构没有被分配模型"

class UserDefaultModelError(str, Enum):
  GET_FAIL = "获取用户的默认模型列表失败"
  CREATE_FAIL = "修改用户的默认模型失败"
  USER_IS_EMPTY = "用户信息不能为空"
  CASE_IS_EMPTY = "使用场景不能为空"
  CASE_IS_INVALID = "无效的使用场景"

class UserError(str, Enum):
  GET_MODEL_FAIL = "获取用户的模型列表失败"
class UploadFileError(str, Enum):
  SAVE_DB_FAIL = "保存文件数据库记录失败"
  SAVE_FILE_FAIL = "保存上传文件失败"
  NOT_RECORD = "文件记录不存在"
  EXCEPTION_WHEN_QUERY = "查询文件记录异常"

class CoVerifyStatus(str, Enum):
  SUCCESS = "SUCCESS"
  FAILED = "FAILED"
  CANCELLED = "CANCELLED"
  NO_START = "NO-START"
  ONGOING = "ONGOING"

class CoAiTracesStatus(str, Enum):
  SUCCESS = "completed"
  FAILED = "failed"
  ONGOING = "processing"

class ChatStatus(str, Enum):
  """AI对话状态枚举"""
  PENDING = "pending"  # 待处理
  PROCESSING = "processing"  # 处理中
  SUCCESS = "success"  # 成功
  ERROR = "error"  # 错误
