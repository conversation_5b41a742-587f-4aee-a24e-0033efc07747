import uuid
from tortoise import fields
from tortoise.models import Model
from app.utils.enum import ChatStatus

class AiChatLog(Model):
    """AI对话日志模型"""
    # 基础字段
    id = fields.UUIDField(pk=True, default=uuid.uuid4, description="主键")
    user_id = fields.UUIDField(description="用户ID")
    user_identifier = fields.CharField(max_length=100, description="Dify用户标识")
    conversation_id = fields.CharField(max_length=100, null=True, description="Dify会话ID")
    message_id = fields.CharField(max_length=100, null=True, description="Dify消息ID")
    query = fields.TextField(description="用户输入的问题")
    status = fields.CharEnumField(ChatStatus, default=ChatStatus.PENDING, description="处理状态")
    error_message = fields.TextField(null=True, description="错误信息")
    time_to_first_token = fields.IntField(null=True, description="首词时间(毫秒)")
    response_time_ms = fields.IntField(null=True, description="响应时间(毫秒)")
    moderation_passed = fields.BooleanField(default=True,description="内容审核是否通过")
    moderation_message = fields.CharField(max_length=500, null=True, description="审核失败原因")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "ai_chat_logs"
        table_description = "AI对话日志表"
