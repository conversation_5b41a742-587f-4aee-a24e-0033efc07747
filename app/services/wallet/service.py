import json
from typing import Op<PERSON>, <PERSON><PERSON>, Any

from app.core.exceptions import PreChargeException
from app.models import SaasUserMapping, SaasPlatform, User, Menu
from app.services.system_config_service import system_config_service
from app.services.wallet.factory import get_wallet_adapter
from app.core.logging import get_logger
from app.services.wallet.interface import PreChargeRequest, ChargeConfirmRequest, ChargeCancelRequest, \
    WalletServiceInterface, AGENT

logger = get_logger(__name__)


class WalletService:
    """
    钱包服务

    根据用户是否在saas_user_mapping表中判断是否需要调用计费接口，
    使用适配器模式处理不同平台的接口差异
    """
    async def _get_wallet_adapter_and_mapping(self, user_id: str) -> Optional[Tuple[WalletServiceInterface, SaasUserMapping]]:
        """私有辅助方法，获取钱包适配器和用户映射"""
        user_mapping = await SaasUserMapping.filter(user_id=user_id).first()
        if not user_mapping:
            return None

        saas_platform = await SaasPlatform.filter(platform=user_mapping.platform, is_active=True).first()
        if not saas_platform:
            logger.error(f"未找到平台 {user_mapping.platform} 的有效配置")
            return None

        adapter = await get_wallet_adapter(saas_platform)
        if not adapter:
            logger.error(f"未找到平台 {saas_platform.platform} 的适配器")
            return None

        return adapter, user_mapping

    async def pre_charge(self, user_id: str, agent_id: str) -> str:
        """
        预计费服务。
        此过程中的任何错误都将直接向上抛出，以便调用方能够感知。

        Args:
            user_id: 用户ID
            agent_id: 智能体标识

        Returns:
            orderId: 预计费响应中的订单ID
        """
        logger.info(f"开始为用户 {user_id} 执行预计费 (Agent: {agent_id})")
        adapter_result = await self._get_wallet_adapter_and_mapping(user_id)
        logger.info(f"预计费服务-获取钱包适配器和用户映射结果: {adapter_result}")
        # 如果结果为 None，说明不是SaaS用户或配置有误，直接返回
        if not adapter_result:
            return None

        adapter, user_mapping = adapter_result
        logger.info(f"为用户 {user_id} 尝试执行预计费 (Agent: {agent_id})")

        try:
            agent_config_key_map = {
                AGENT.CHAT.value: "AGENT_EXPIRE_MINUTES_CHAT",
                AGENT.HOMEWORK.value: "AGENT_EXPIRE_MINUTES_HOMEWORK",
                AGENT.PAPER.value: "AGENT_EXPIRE_MINUTES_PAPER",
                AGENT.PPT.value: "AGENT_EXPIRE_MINUTES_PPT",
                AGENT.AI_TRACES.value: "AGENT_EXPIRE_MINUTES_TRACES",
                AGENT.HALLUCINATION.value: "AGENT_EXPIRE_MINUTES_HALLUCINATION",
            }

            # 预计费默认超时时间60分钟
            expire_minutes = 60

            config_key = agent_config_key_map.get(agent_id)

            if config_key:
                config_value = await system_config_service.get_config(config_key)
                expire_minutes = int(config_value)

            precharge_request = PreChargeRequest(
                user_id=user_id,
                external_id=user_mapping.external_id,
                agent_id=agent_id,
                expire_minutes=expire_minutes
            )
            result = await adapter.pre_charge(precharge_request)
            if result.success:
                logger.info(f"用户 {user_id} 预计费成功，订单ID: {result.order_id}")
                return result.order_id
            else:
                logger.error(f"用户 {user_id} 预计费API调用失败: {result.message}")
                raise PreChargeException(result.message)
        except PreChargeException as e:
            raise e
        except Exception as e:
            error_msg = f"预计费流程失败: {e}"
            logger.error(error_msg)
            raise Exception("系统繁忙，请稍后重试！")

    async def charge_confirm(self, user_id: str, order_id: str, business_id: str) -> None:
        """
        计费确认。
        """
        logger.info(f"开始为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费确认")
        if not order_id:
            return  # 如果没有订单ID，静默返回

        logger.info(f"开始为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费确认")
        try:
            adapter_result = await self._get_wallet_adapter_and_mapping(user_id)
            if not adapter_result:
                return  # 虽然有order_id，但如果此时配置失效，也应静默失败

            adapter, _ = adapter_result

            confirm_request = ChargeConfirmRequest(
                order_id=order_id,
                business_id=business_id
            )

            await adapter.confirm(confirm_request)
            logger.info(f"用户 {user_id} 的订单 {order_id} 计费确认请求已成功发送")
        except Exception as e:
            logger.error(f"为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费确认失败: {e}", exc_info=True)


    async def charge_cancel(self, user_id: str, order_id: str, business_id: str) -> None:
        """
        计费取消。
        """
        logger.info(f"开始为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费取消")
        if not order_id:
            return  # 如果没有订单ID，静默返回

        logger.info(f"开始为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费取消")
        try:
            adapter_result = await self._get_wallet_adapter_and_mapping(user_id)
            if not adapter_result:
                return

            adapter, _ = adapter_result

            cancel_request = ChargeCancelRequest(
                order_id=order_id,
                business_id=business_id
            )

            await adapter.cancel(cancel_request)
            logger.info(f"用户 {user_id} 的订单 {order_id} 计费取消请求已成功发送")
        except Exception as e:
            logger.error(f"为用户 {user_id} 的订单 {order_id} (业务ID: {business_id}) 执行计费取消失败: {e}", exc_info=True)


    async def get_saas_platform(self, platform: str) -> Optional[SaasPlatform]:
        """
        获取指定的SaaS平台配置
        """
        return await SaasPlatform.filter(
            platform=platform,
            is_active=True
        ).first()

    def extract_conversation_id_from_chunk(self, chunk: Any) -> Optional[str]:
        """
        从 Dify SSE 流式数据块中提取 'conversation_id'
        """
        try:
            if chunk.startswith('data: '):
                json_str = chunk[len('data: '):].strip()

                if json_str and json_str != '[DONE]':
                    data = json.loads(json_str)
                    if isinstance(data, dict):
                        conversation_id = data.get('conversation_id')
                        return conversation_id
        except Exception as e:
            logger.error(f"解析chuck失败: {chunk}，{e}")
            return None

    def extract_message_id_from_chunk(self, chunk: Any) -> Optional[tuple[str, str]]:
        """
        从 Dify SSE 流式数据块中提取 'conversation_id_message_id' 下划线拼接
        """
        try:
            if chunk.startswith('data: '):
                json_str = chunk[len('data: '):].strip()

                if json_str and json_str != '[DONE]':
                    data = json.loads(json_str)
                    if isinstance(data, dict):
                        conversation_id = data.get('conversation_id')
                        message_id = data.get('message_id')
                        return conversation_id, message_id
        except Exception as e:
            logger.error(f"解析chuck失败: {chunk}，{e}")
            return None

# 全局服务实例
wallet_service = WalletService()