import asyncio
from datetime import datetime

from app.services.text_moderation_service import text_moderation_service
from fastapi import APIRouter, Request, HTTPException, status, UploadFile, File, Path
from fastapi.responses import StreamingResponse
from typing import Optional
import json

from app.core.config import settings
from app.core.logging import get_logger
from app.api.deps import get_current_user_from_state
from app.services.dify_service import DifyService, create_dify_service
from app.services.ai_chat_log_service import ai_chat_log_service, ChatStatus
from app.api.schemas.ai_chat import (
    ChatMessageRequest, 
    ConversationListResponse,
    FileUploadResponse,
    StopChatMessageResponse,
    MessagesListResponse,
    DeleteConversationResponse,
    RenameConversationRequest,
    RenameConversationResponse
)
from app.services.wallet.interface import AGENT
from app.services.wallet.service import wallet_service
from app.utils.utils import read_file_content, send_data, ResponseModel
from app.services.system_config_service import system_config_service
from app.services.content_moderation_service import content_moderation_service

# 获取logger实例
logger = get_logger(__name__)

router = APIRouter()

async def get_dify_service() -> DifyService:
    """获取Dify服务实例"""
    if not settings.DIFY_AI_CHAT_API_KEY or not settings.DIFY_API_URL:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Dify API配置未设置，请检查环境变量DIFY_AI_CHAT_API_KEY和DIFY_API_URL"
        )
    return await create_dify_service(settings.DIFY_AI_CHAT_API_KEY, settings.DIFY_API_URL)

@router.post("/chat", summary="AI对话接口（流式输出）")
async def chat_with_ai(
    request: Request,
    chat_request: ChatMessageRequest
):
    """
    创建AI对话消息，支持流式输出
    
    Args:
        chat_request: 对话请求参数
        
    Returns:
        StreamingResponse: 流式响应，SSE格式
    """
    # 用于saas计费
    order_id = None
    business_id = None
    current_user = None
    is_pre_charge = False
    needs_new_charge = True
    chat_log = None
    start_time = datetime.now()

    try:
        # 获取Dify服务
        dify_service = await get_dify_service()

        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 发起AI对话: {chat_request.query[:50]}...")

        # 构建用户标识（使用用户ID作为唯一标识）
        user_identifier = f"user_{current_user.id}"
        logger.info(f"chat_request================= {chat_request.files} 构建用户标识: {user_identifier}")

        # 内容审核 - 检查用户输入的文本内容
        moderation_passed = True
        moderation_message = None
        if chat_request.query and chat_request.query.strip():
            is_safe, error_msg = await content_moderation_service.verify_text_content(
                content=chat_request.query,
                description="AI对话内容"
            )
            if not is_safe:
                moderation_passed = False
                moderation_message = error_msg
                logger.warning(f"用户 {current_user.username} 输入内容审核未通过: {chat_request.query[:100]}...")
                # 内容审核失败，返回400状态码
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=error_msg
                )
            logger.info(f"用户 {current_user.username} 输入内容审核通过")
                
          

        # 判断是否需要重新计费，如果提供了conversation_id，则检查是否为新对话
        if chat_request.conversation_id:
            message_count = await ai_chat_log_service.get_conversation_message_count(current_user.id, chat_request.conversation_id)
            max_messages_per_round = int(await system_config_service.get_config("chat_max_messages_per_round"))
            if message_count % max_messages_per_round == 0:
                needs_new_charge = True
                logger.info(f"用户 {current_user.username} 当前为第{message_count + 1}次对话，需要重新计费")
            else:
                needs_new_charge = False
                logger.info(f"用户 {current_user.username} 当前为第{message_count + 1}次对话，无需重新计费")
        else:
            logger.info(f"用户 {current_user.username} 当前会话为新会话，需要重新计费")

        # 如果需要重新计费，进行预扣费
        is_pre_charge = False
        if needs_new_charge:
            logger.info(f"用户 {current_user.username} 需要重新计费，开始预扣费")
            try:
                order_id = await wallet_service.pre_charge(current_user.id, AGENT.CHAT.value)
                is_pre_charge = True
            except Exception as e:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=str(e)
                )
        else:
            logger.info(f"用户 {current_user.username} 当前轮次内，无需重新计费")
        
        # 修正文件类型，将custom类型修改为document类型
        if chat_request.files:
            for file in chat_request.files:
                if file.type == "custom" and file.transfer_method == "local_file":
                    file.type = "document"
                    logger.info(f"文件类型已从 'custom' 修正为 'document'")

        # 创建AI对话日志记录
        try:
            chat_log = await ai_chat_log_service.create_chat_log(
                user=current_user,
                chat_request=chat_request,
                user_identifier=user_identifier,
                moderation_passed=moderation_passed,
                moderation_message=moderation_message
            )
            logger.info(f"创建AI对话日志记录: {chat_log.id}")
        except Exception as log_error:
            logger.error(f"创建AI对话日志失败: {str(log_error)}")

        # 调用Dify服务发送消息
        if chat_request.response_mode == "streaming":
            # 流式模式
            try:
                stream_generator = await dify_service.send_chat_message(
                    query=chat_request.query,
                    user=user_identifier,
                    inputs=chat_request.inputs,
                    response_mode=chat_request.response_mode,
                    conversation_id=chat_request.conversation_id,
                    files=chat_request.files,
                    auto_generate_name=chat_request.auto_generate_name
                )
                
                logger.info(f"开始流式响应AI对话，用户: {current_user.username}")
                
                # 包装流式生成器以确保异常安全
                async def safe_stream_wrapper():
                    is_first_chunk = True
                    is_confirmed = False
                    try:
                        async for chunk in stream_generator:
                            # 第一块响应内容返回时更新日志为进行中
                            if is_first_chunk and chat_log:
                                time_to_first_token = (datetime.now() - start_time).total_seconds() * 1000
                                conversation_id, message_id = wallet_service.extract_message_id_from_chunk(chunk)
                                asyncio.create_task(ai_chat_log_service.update_chat_log_processing(chat_log, time_to_first_token, conversation_id, message_id))
                                is_first_chunk = False
                            # 异步 计费确认
                            if not is_confirmed and is_pre_charge:
                                conversation_id, message_id = wallet_service.extract_message_id_from_chunk(chunk)
                                business_id = f"{conversation_id}_{message_id}"
                                if business_id:
                                    asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, business_id))
                                    is_confirmed = True
                                    logger.info(f"用户 {current_user.username} 计费确认完成，business_id: {business_id}")
                            yield chunk
                    except Exception as inner_error:
                        # 异步 计费取消
                        if is_pre_charge:
                            asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                        logger.error(f"流式响应过程中发生错误: {str(inner_error)}")
                        error_content = f"流式响应错误: {str(inner_error)}"
                        yield f"data: {json.dumps({'status': 'error', 'content': error_content})}\n\n"
                    else:
                        # 流式响应结束
                        if chat_log:
                            response_time_ms = (datetime.now() - start_time).total_seconds() * 1000
                            # 更新日志为成功
                            asyncio.create_task(ai_chat_log_service.update_chat_log_success(chat_log, response_time_ms))
                return StreamingResponse(
                    safe_stream_wrapper(),
                    media_type="text/event-stream",
                    headers={
                        "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "X-Accel-Buffering": "no"
                    }
                )
            except Exception as stream_error:
                # 流式生成器创建失败的错误处理
                # 异步 计费取消
                if is_pre_charge:
                    asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                error_msg = f"创建流式响应失败: {str(stream_error)}"
                logger.error(f"用户 {current_user.username} 创建流式响应失败: {error_msg}")
                
                async def error_stream():
                    try:
                        yield f"data: {json.dumps({'status': 'error', 'content': error_msg})}\n\n"
                    except Exception as json_error:
                        logger.error(f"JSON序列化错误: {json_error}")
                        yield f"data: {{'status': 'error', 'content': 'Internal server error'}}\n\n"
                
                return StreamingResponse(
                    error_stream(),
                    media_type="text/event-stream",
                    status_code=500
                )
        else:
            # 阻塞模式
            try:
                result = await dify_service.send_chat_message(
                    query=chat_request.query,
                    user=user_identifier,
                    inputs=chat_request.inputs,
                    response_mode=chat_request.response_mode,
                    conversation_id=chat_request.conversation_id,
                    files=chat_request.files,
                    auto_generate_name=chat_request.auto_generate_name
                )
                
                logger.info(f"AI对话完成，用户: {current_user.username}, 消息ID: {result.get('message_id')}")
                logger.info(f"AI对话完成，result: {result} ")
                logger.info(f"AI对话完成，result type: {isinstance(result, dict)}")
                # 确保result是可序列化的    
                if result is None:
                    logger.warning("Dify服务返回了None结果")
                    result = {"message": "响应为空"}

                # 异步 计费确认
                if is_pre_charge:
                    business_id = result.get("conversation_id")
                    asyncio.create_task(wallet_service.charge_confirm(current_user.id, order_id, business_id))
                
                return send_data(True, result)
                
            except Exception as dify_error:
                error_msg = f"Dify服务错误: {str(dify_error)}"
                logger.error(f"用户 {current_user.username} Dify服务调用失败: {error_msg}")
                # 异步 计费取消
                if is_pre_charge:
                    asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
                return send_data(False, None, error_msg)
            
    except HTTPException:
        # 重新抛出HTTP异常，让FastAPI自己处理状态码
        raise
    except Exception as e:
        # 异步 计费取消
        if is_pre_charge:
            asyncio.create_task(wallet_service.charge_cancel(current_user.id, order_id, business_id))
        error_msg = f"AI对话失败: {str(e)}"
        username = current_user.username if current_user else 'Unknown'
        logger.error(f"用户 {username} AI对话失败: {error_msg}")
        logger.exception("详细异常信息:")  # 记录完整的异常堆栈
        # 更新日志为错误状态
        if chat_log:
            response_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            await ai_chat_log_service.update_chat_log_error(
                chat_log,
                error_message=f"AI对话失败: {str(e)}",
                status=ChatStatus.ERROR,
                response_time_ms=response_time_ms
            )
        
        try:
            if hasattr(chat_request, 'response_mode') and chat_request.response_mode == "streaming":
                # 流式模式下返回错误事件
                async def error_stream():
                    try:
                        yield f"data: {json.dumps({'status': 'error', 'content': error_msg})}\n\n"
                    except Exception as json_error:
                        logger.error(f"错误流JSON序列化失败: {json_error}")
                        yield f"data: {{'status': 'error', 'content': 'Critical error'}}\n\n"
                
                return StreamingResponse(
                    error_stream(),
                    media_type="text/event-stream",
                    status_code=500
                )
            else:
                return send_data(False, None, error_msg)
        except Exception as final_error:
            # 最后的兜底处理
            logger.error(f"最终异常处理失败: {str(final_error)}")
            # 返回一个最简单的错误响应
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="服务器内部错误"
            )


@router.get("/conversations", response_model=ResponseModel[ConversationListResponse], summary="获取用户会话列表")
async def get_user_conversations(
    request: Request,
    last_id: Optional[str] = None,
    limit: int = 20,
    pinned: Optional[bool] = None
):
    """
    获取当前用户的会话列表
    
    Args:
        last_id: 上一页最后一条记录的 ID（用于分页）
        limit: 返回数量限制，默认20
        pinned: 是否只返回置顶会话
        
    Returns:
        用户的会话列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取会话列表")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 获取会话列表
        filter_days_str = await system_config_service.get_config("history_record_filter_days")
        filter_days = int(filter_days_str) if filter_days_str and filter_days_str.isdigit() else 30
        result = await dify_service.get_conversations(
            user=user_identifier,
            last_id=last_id,
            limit=limit,
            pinned=pinned,
            filter_days=filter_days  # 只获取配置天数内的会话
        )
        
        logger.info(f"成功获取用户 {current_user.username} 的会话列表，数量: {len(result.get('data', []))}")
        return send_data(True, result)
        
    except Exception as e:
        error_msg = f"获取会话列表失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取会话列表失败: {error_msg}")
        return send_data(False, None, error_msg)

@router.post("/upload-multipart", response_model=ResponseModel[FileUploadResponse], summary="上传文件到Dify（支持文件上传）")
async def upload_file_multipart(
    request: Request,
    file: UploadFile = File(...)
):
    """
    通过multipart/form-data上传文件到Dify API
    
    Args:
        file: 上传的文件
        
    Returns:
        上传成功后的文件信息
    """
    import tempfile
    import os
    import mimetypes
    
    # 获取当前用户
    current_user = get_current_user_from_state(request)
    temp_file_path = None
    converted_docx_path = None
    
    try:
        logger.info(f"用户 {current_user.username} 开始上传文件: {file.filename}, 大小: {file.size if hasattr(file, 'size') else 'unknown'}")
        
        # 文件大小检查（限制50MB）
        max_file_size = 50 * 1024 * 1024  # 50MB
        if hasattr(file, 'size') and file.size and file.size > max_file_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"文件大小超过限制，最大允许50MB，当前文件大小: {file.size / (1024*1024):.2f}MB"
            )
        
        # 文件类型检查
        allowed_extensions = {
            # 文档类型
            '.txt', '.markdown', '.mdx', '.pdf', '.html', '.htm', '.xlsx', '.xls', '.docx', '.doc', '.csv', '.vtt', '.properties', '.md',
            # 图片类型
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.tiff', '.tif', '.ico', '.jfif', '.pjpeg', '.pjp'
        }
        
        # 检查文件扩展名
        if file.filename:
            file_ext = os.path.splitext(file.filename.lower())[1]
            if file_ext not in allowed_extensions:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"不支持的文件类型: {file_ext}。支持的文件类型包括: {', '.join(sorted(allowed_extensions))}"
                )
            
            logger.info(f"文件类型验证通过: {file_ext}")
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件名不能为空"
            )
        
        # 读取文件内容
        content = await file.read()
        logger.debug(f"读取文件内容，大小: {len(content)} 字节")
        
        if not content or not content.strip():
            error_msg = "文件内容为空"
            return send_data(False, None, error_msg)
        
        # 创建临时文件
        temp_dir = tempfile.gettempdir()
        safe_filename = file.filename or f"upload_{current_user.id}"
        # 确保文件名安全
        safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in '._-')
        temp_file_path = os.path.join(temp_dir, f"dify_upload_{current_user.id}_{safe_filename}")
        
        # 写入文件内容
        with open(temp_file_path, "wb") as temp_file:
            temp_file.write(content)
            
        logger.debug(f"临时文件已保存: {temp_file_path}")
        
        # 处理 .doc 文件转换为 .docx
        if file_ext == '.doc':
            try:
                from app.utils.document_parser import convert_doc_to_docx_with_libreoffice
                
                logger.info(f"检测到 .doc 文件，开始使用 LibreOffice 转换为 .docx 格式")
                converted_docx_path =  convert_doc_to_docx_with_libreoffice(temp_file_path)
                logger.info(f".doc 文件转换成功: {converted_docx_path}")
                
                # 使用转换后的 .docx 文件进行后续处理
                temp_file_path = converted_docx_path
                
                # 更新文件名，将扩展名改为.docx
                original_filename = file.filename
                new_filename = os.path.splitext(original_filename)[0] + '.docx'
                file.filename = new_filename
                logger.info(f"文件名已更新: {original_filename} -> {new_filename}")
                
            except Exception as convert_error:
                logger.error(f".doc 文件转换失败: {str(convert_error)}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f".doc 文件转换失败，请确保文件格式正确: {str(convert_error)}"
                )
        
        # 文件内容审核 - 对文档类型文件进行内容审核
        if file_ext in ['.txt', '.md', '.markdown', '.docx', '.doc', '.pdf']:
            # 使用统一的内容审核服务
            # 读取文件内容
            document_text = read_file_content(temp_file_path)
            
            # 检查提取的文本是否为空
            if not document_text.strip():
                return send_data(False, None, "文档中没有提取到文本内容")
            
            # 调用文本审核服务
            is_content_safe = await text_moderation_service.is_content_safe(
                content=document_text
            )
            
            if not is_content_safe:
                logger.warning(f"用户 {current_user.username} 上传文件内容审核未通过: {file.filename}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="您输入的文档/文本内容违规"
                )
            logger.info(f"用户 {current_user.username} 上传文件内容审核通过")
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务上传文件
        result = await dify_service.upload_file(
            file_path=temp_file_path,
            user=user_identifier
        )
        
        # 检查并修正文件类型
        if file_ext in ['.doc', '.docx', '.pdf', '.txt', '.md', '.markdown']:
            if result.get('type') == 'custom':
                # 如果类型被错误地标记为custom，修改为document
                result['type'] = 'document'
                logger.info(f"文件类型已从 'custom' 修正为 'document'")
        
        logger.info(f"用户 {current_user.username} 文件上传成功，文件ID: {result.get('id')}, 文件名: {result.get('name')}, 类型: {result.get('type')}")
        return send_data(True, result)
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        error_msg = f"文件上传失败: {str(e)}"
        logger.error(f"用户 {current_user.username} 文件上传失败: {error_msg}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=error_msg
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.debug(f"已清理临时文件: {temp_file_path}")
            except Exception as cleanup_error:
                logger.warning(f"清理临时文件失败: {cleanup_error}")
                
        # 清理转换后的 .docx 文件和其临时目录（如果与临时文件路径不同）
        if converted_docx_path and converted_docx_path != temp_file_path and os.path.exists(converted_docx_path):
            try:
                # 获取转换文件的临时目录
                temp_dir = os.path.dirname(converted_docx_path)
                
                # 删除转换后的文件
                os.remove(converted_docx_path)
                logger.debug(f"已清理转换后的 .docx 文件: {converted_docx_path}")
                
                # 如果是临时目录且为空，则删除目录
                if (temp_dir and 
                    temp_dir != tempfile.gettempdir() and 
                    'libreoffice_convert_' in os.path.basename(temp_dir)):
                    try:
                        os.rmdir(temp_dir)  # 只删除空目录
                        logger.debug(f"已清理转换临时目录: {temp_dir}")
                    except OSError:
                        # 目录不为空或其他错误，忽略
                        pass
                        
            except Exception as cleanup_error:
                logger.warning(f"清理转换后的 .docx 文件失败: {cleanup_error}")

@router.post("/chat-messages/{task_id}/stop", response_model=ResponseModel[StopChatMessageResponse], summary="停止对话消息生成")
async def stop_chat_message(
    request: Request,
    task_id: str
):
    """
    停止流式对话消息生成
    
    Args:
        task_id: 任务ID，可在流式返回Chunk中获取
        
    Returns:
        停止操作的结果
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求停止对话消息，task_id: {task_id}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识（使用用户ID作为唯一标识）
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务停止消息
        result = await dify_service.stop_chat_message(
            task_id=task_id,
            user=user_identifier
        )
        
        logger.info(f"成功停止对话消息，用户: {current_user.username}, task_id: {task_id}")
        return send_data(True, result)
        
    except Exception as e:
        error_msg = f"停止对话消息失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 停止对话消息失败: {error_msg}")
        return send_data(False, None, error_msg)

@router.get("/messages", response_model=ResponseModel[MessagesListResponse], summary="获取会话历史消息")
async def get_conversation_messages(
    request: Request,
    conversation_id: str,
    first_id: Optional[str] = None,
    limit: int = 20
):
    """
    获取会话历史消息
    滚动加载形式返回历史聊天记录，第一页返回最新 limit 条，即：倒序返回
    
    Args:
        conversation_id: 会话ID，必填
        first_id: 第一条消息ID（用于分页加载）
        limit: 返回消息数量限制，默认20
        
    Returns:
        会话历史消息列表
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 获取会话历史消息，会话ID: {conversation_id}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 获取会话历史消息
        result = await dify_service.get_messages(
            conversation_id=conversation_id,
            user=user_identifier,
            first_id=first_id,
            limit=limit
        )
        
        logger.info(f"成功获取用户 {current_user.username} 的会话历史消息，数量: {len(result.get('data', []))}")
        return send_data(True, result)
        
    except Exception as e:
        error_msg = f"获取会话历史消息失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 获取会话历史消息失败: {error_msg}")
        return send_data(False, None, error_msg) 

@router.delete("/conversations/{conversation_id}", response_model=ResponseModel[DeleteConversationResponse], summary="删除会话")
async def delete_conversation(
    request: Request,
    conversation_id: str = Path(..., description="会话ID")
):
    """
    删除指定会话
    
    Args:
        conversation_id: 会话ID
        
    Returns:
        删除操作的结果
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求删除会话，会话ID: {conversation_id}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务删除会话
        result = await dify_service.delete_conversation(
            conversation_id=conversation_id,
            user=user_identifier
        )
        
        # 检查Dify服务返回的结果
        if result.get("result") == "success" or result.get("message") == "会话不存在或已被删除":
            logger.info(f"成功删除会话，用户: {current_user.username}, 会话ID: {conversation_id}")
            return send_data(True, {
                "result": "success",
                "conversation_id": conversation_id,
                "message": result.get("message", "会话已删除")
            })
        else:
            # 如果Dify服务返回了其他结果，直接返回
            logger.info(f"删除会话完成，用户: {current_user.username}, 会话ID: {conversation_id}, 结果: {result}")
            return send_data(True, result)
        
    except Exception as e:
        error_msg = f"删除会话失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 删除会话失败: {error_msg}")
        return send_data(False, None, error_msg)

@router.post("/conversations/{conversation_id}/name", response_model=ResponseModel[RenameConversationResponse], summary="重命名会话")
async def rename_conversation(
    request: Request,
    rename_request: RenameConversationRequest,
    conversation_id: str = Path(..., description="会话ID")
):
    """
    重命名指定会话
    
    Args:
        conversation_id: 会话ID
        rename_request: 重命名请求参数
        
    Returns:
        重命名操作的结果
    """
    try:
        # 获取当前用户
        current_user = get_current_user_from_state(request)
        logger.info(f"用户 {current_user.username} 请求重命名会话，会话ID: {conversation_id}, 新名称: {rename_request.name}")
        
        # 获取Dify服务
        dify_service = await get_dify_service()
        
        # 构建用户标识
        user_identifier = f"user_{current_user.id}"
        
        # 调用Dify服务重命名会话
        result = await dify_service.rename_conversation(
            conversation_id=conversation_id,
            user=user_identifier,
            name=rename_request.name,
            auto_generate=rename_request.auto_generate
        )
        
        logger.info(f"成功重命名会话，用户: {current_user.username}, 会话ID: {conversation_id}")
        return send_data(True, {
            "result": "success",
            "conversation_id": conversation_id,
            "name": result.get("name", rename_request.name)
        })
        
    except Exception as e:
        error_msg = f"重命名会话失败: {str(e)}"
        logger.error(f"用户 {current_user.username if 'current_user' in locals() else 'Unknown'} 重命名会话失败: {error_msg}")
        return send_data(False, None, error_msg) 