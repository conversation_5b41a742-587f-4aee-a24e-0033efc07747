from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "ai_chat_logs" (
    "id" UUID NOT NULL PRIMARY KEY,
    "user_id" UUID NOT NULL,
    "user_identifier" VARCHAR(100) NOT NULL,
    "conversation_id" VARCHAR(100),
    "message_id" VARCHAR(100),
    "query" TEXT NOT NULL,
    "status" VARCHAR(10) NOT NULL DEFAULT 'pending',
    "error_message" TEXT,
    "time_to_first_token" INT,
    "response_time_ms" INT,
    "moderation_passed" BOOL NOT NULL DEFAULT True,
    "moderation_message" VARCHAR(500),
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "ai_chat_logs"."id" IS '主键';
COMMENT ON COLUMN "ai_chat_logs"."user_id" IS '用户ID';
COMMENT ON COLUMN "ai_chat_logs"."user_identifier" IS 'Dify用户标识';
COMMENT ON COLUMN "ai_chat_logs"."conversation_id" IS 'Dify会话ID';
COMMENT ON COLUMN "ai_chat_logs"."message_id" IS 'Dify消息ID';
COMMENT ON COLUMN "ai_chat_logs"."query" IS '用户输入的问题';
COMMENT ON COLUMN "ai_chat_logs"."status" IS '处理状态';
COMMENT ON COLUMN "ai_chat_logs"."error_message" IS '错误信息';
COMMENT ON COLUMN "ai_chat_logs"."time_to_first_token" IS '首词时间(毫秒)';
COMMENT ON COLUMN "ai_chat_logs"."response_time_ms" IS '响应时间(毫秒)';
COMMENT ON COLUMN "ai_chat_logs"."moderation_passed" IS '内容审核是否通过';
COMMENT ON COLUMN "ai_chat_logs"."moderation_message" IS '审核失败原因';
COMMENT ON COLUMN "ai_chat_logs"."created_at" IS '创建时间';
COMMENT ON COLUMN "ai_chat_logs"."updated_at" IS '更新时间';
COMMENT ON TABLE "ai_chat_logs" IS 'AI对话日志表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "ai_chat_logs";"""
